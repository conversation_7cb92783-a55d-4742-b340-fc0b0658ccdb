import os
os.environ["CUDA_VISIBLE_DEVICES"]="0"
print(f'CUDA_VISIBLE_DEVICES {os.environ["CUDA_VISIBLE_DEVICES"]}')
import pandas as pd
import numpy as np
from tqdm import tqdm
from sentence_transformers import SentenceTransformer

# Load the SentenceTransformer model
print("Loading Qwen/Qwen3-Embedding-8B...")
model = SentenceTransformer('Qwen/Qwen3-Embedding-8B')
print("Model loaded successfully")

# Load your dataframe
df = pd.read_pickle('./icd_with_note.plk')

print("Generating embeddings...")

# Set batch size for processing large lists
BATCH_SIZE = 32 # Adjust this based on your GPU memory

def process_text_list_in_batches(text_list, batch_size):
    """Process a list of texts in batches and return all embeddings"""
    all_embeddings = []
    
    for i in range(0, len(text_list), batch_size):
        batch = text_list[i:i + batch_size]
        batch_embeddings = model.encode(batch)
        all_embeddings.extend(batch_embeddings)
    
    return np.array(all_embeddings)

averaged_embeddings = []
individual_embeddings = []

for text in tqdm(df['TEXT']):
    if isinstance(text, list) and len(text) > 0:
        if len(text) > 5:
            # Process in batches for lists with more than 5 elements
            text_embeddings = process_text_list_in_batches(text, BATCH_SIZE)
        else:
            # Process normally for lists with 5 or fewer elements
            text_embeddings = model.encode(text)
        
        # Store individual embeddings for each element in the list
        individual_embeddings.append(text_embeddings.tolist())
        
        # Average the embeddings
        avg_embedding = np.mean(text_embeddings, axis=0)
        averaged_embeddings.append(avg_embedding)
        
    elif isinstance(text, str):
        # For single strings
        embedding = model.encode([text])[0]
        
        # For single strings, individual embedding is the same as the single embedding
        individual_embeddings.append([embedding.tolist()])
        averaged_embeddings.append(embedding)
        
    else:
        # Handle empty or None values with zero embedding
        embedding_dim = model.get_sentence_embedding_dimension()
        zero_embedding = np.zeros(embedding_dim)
        
        individual_embeddings.append([zero_embedding.tolist()])
        averaged_embeddings.append(zero_embedding)

# Convert to numpy array for averaged embeddings
averaged_embeddings = np.array(averaged_embeddings)

# Add both columns to the dataframe
df["individual_embeddings"] = individual_embeddings  # List of embeddings for each text element
df["average_embedding"] = list(averaged_embeddings)  # Single averaged embedding per row

# Save the dataframe with both embedding types
df.to_pickle('./icd_with_embedding.plk')
df.to_csv('./icd_with_embedding.csv')

print(f"Saved dataframe with {len(df)} rows")
print(f"Embedding dimension: {averaged_embeddings.shape[1]}")
print(f"Individual embeddings column contains lists of embeddings for each text element")
print(f"Average embedding column contains single averaged embedding per row")
print(f"Used batch processing for lists with more than 5 elements (batch size: {BATCH_SIZE})")