import re
import sys
import os
import numpy as np
import pandas as pd
from tqdm import tqdm
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import MultiLabelBinarizer
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score,confusion_matrix
import xgboost as xgb
np.random.seed(42)

# Create fold directory if it doesn't exist
os.makedirs("fold_icd_code", exist_ok=True)
def evaluate(y_true, y_pred, save_path,title="ICD code", plot_cm=True):
    conf_matrix = confusion_matrix(y_true, y_pred)
    # Reorder confusion matrix to have positive class first
    reordered_conf_matrix = np.array(
        [
            [conf_matrix[1, 0], conf_matrix[1, 1]],
            [conf_matrix[0, 0], conf_matrix[0, 1]],
        ]
    )
    cm_df = pd.DataFrame(
        reordered_conf_matrix, index=["1", "0"], columns=["0", "1"]
    )
    if plot_cm:
        plt.figure(figsize=(8, 6))
        sns.heatmap(
            cm_df,
            annot=True,
            fmt="d",
            cmap="Blues",
            cbar=True,
            linewidths=1,
            linecolor="black",
        )
        plt.ylabel("True Label")
        plt.xlabel("Predicted Label")
        plt.title(title)
        plt.savefig(save_path, bbox_inches="tight", dpi=300)
        print(f"Confusion matrix saved to {save_path}")

        # plt.show()
        plt.close()

file_path = "./datasets/1.4/DIAGNOSES_ICD.csv"
df = pd.read_csv(file_path)

print(f"Length of icd_unique: {df['ICD9_CODE'].nunique()}")
df = df.groupby(['SUBJECT_ID']).agg({"HADM_ID": list, "ROW_ID": list,'SEQ_NUM': list, 'ICD9_CODE': list,}).reset_index()

cleaned_codes = df["ICD9_CODE"].apply(
    lambda x: [code for code in x if pd.notna(code)] if isinstance(x, list) else []
)
mlb = MultiLabelBinarizer()
# Fit and transform the list column
one_hot_encoding = pd.DataFrame(
    mlb.fit_transform(cleaned_codes),
    columns=mlb.classes_,
    dtype=int
)

# Define self_harm codes
icd9_self_harm = r"E9500|E9501|E9502|E9503|E9504|E9505|E9506|E9507|E9509|E9530|E9538|E954|E9550|E9554|E9559|E956|E9570|E9571|E9572|E9579|E9580|E9581|E9583|E9585|E9588|E9589|E959"
col_to_keep = [col for col in one_hot_encoding.columns if not re.match(icd9_self_harm, str(col))]
one_hot_encoding = one_hot_encoding[col_to_keep]
df["one_hot_encoding"] = list(one_hot_encoding.values) 
df["labels"] = df["ICD9_CODE"].apply(
    lambda x: int(any(re.search(icd9_self_harm, str(code)) for code in x if pd.notna(code))) 
    if isinstance(x, list) else 0
)
l = sum(df["labels"])
print(f"Length of unique codes: {l}")

# Print class distribution before balancing
print(f"Original class distribution:")
print(f"Class 0: {sum(df['labels'] == 0)}")
print(f"Class 1: {sum(df['labels'] == 1)}")

# Prepare data for modeling
x = np.asarray(one_hot_encoding)
y = np.array(df["labels"])

# Calculate scale_pos_weight for class imbalance
scale_pos_weight = (len(y) - sum(y)) / sum(y)
print(f"Scale pos weight: {scale_pos_weight:.2f}")

# Define XGBoost classifier
model = xgb.XGBClassifier(
    objective='binary:logistic',
    n_estimators=200,
    max_depth=6,  
    learning_rate=0.01,
    reg_alpha=0.1,
    reg_lambda=0.1,
    random_state=42,
    scale_pos_weight=scale_pos_weight  
)

# Perform 5-fold cross-validation
print("\nPerforming 5-fold cross-validation...")
skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

# Store results for each fold
cv_results = {
    'accuracy': [],
    'precision': [],
    'recall': [],
    'f1': [],
}

# List to store all fold results for combined output
all_fold_results = []

fold = 1
for train_idx, val_idx in tqdm(skf.split(x, y), total=5, desc="Cross-validation folds"):
    
    X_train_fold, X_val_fold = x[train_idx], x[val_idx]
    y_train_fold, y_val_fold = y[train_idx], y[val_idx]
    
    # Train model
    model.fit(X_train_fold, y_train_fold)
    
    # Make predictions
    y_pred = model.predict(X_val_fold)
    y_pred_proba = model.predict_proba(X_val_fold)
    
    # Create DataFrame for this fold's results
    fold_df = pd.DataFrame({
        'SUBJECT_ID': df.iloc[val_idx]['SUBJECT_ID'],
        'labels': y_val_fold,
        'pred': y_pred,
        'prob_1': y_pred_proba[:, 1], # Probability of class 1
        'icd9_code': df.iloc[val_idx]["ICD9_CODE"],
    })

    all_fold_results.append(fold_df)    
    fold += 1

# Combine all fold results into a single DataFrame
df_combined = pd.concat(all_fold_results, ignore_index=True)
df_combined_path = os.path.join("fold_results", "combined_5fold_results.csv")
df_combined.to_csv(df_combined_path, index=False)

# Calculate metrics
evaluate(y_true=df_combined["labels"], y_pred=df_combined["pred"], save_path=f"fold_results/cm.png")
accuracy = accuracy_score(y_true=df_combined["labels"], y_pred=df_combined["pred"])
precision = precision_score(y_true=df_combined["labels"], y_pred=df_combined["pred"])
recall = recall_score(y_true=df_combined["labels"], y_pred=df_combined["pred"])
f1 = f1_score(y_true=df_combined["labels"], y_pred=df_combined["pred"])
print(f"Accuracy: {accuracy:.4f}")
print(f"Precision: {precision:.4f}")
print(f"Recall: {recall:.4f}")
print(f"F1-score: {f1:.4f}")

print(f"\nCombined results from all 5 folds saved to {df_combined_path}")
