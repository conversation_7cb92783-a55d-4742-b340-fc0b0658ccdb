import numpy as np
import pandas as pd
import re
from sklearn.preprocessing import MultiLabelBinarizer

np.random.seed(45)

# Load diagnosis data
file_path1 = "./datasets/1.4/DIAGNOSES_ICD.csv"
df1 = pd.read_csv(file_path1)
df1 = df1.groupby(['SUBJECT_ID']).agg({
    "ROW_ID": list,
    'SEQ_NUM': list, 
    'ICD9_CODE': list,
}).reset_index()

# Load notes data with dtype fix
file_path2 = "./datasets/1.4/NOTEEVENTS.csv"
df2 = pd.read_csv(file_path2, low_memory=False)
df2_grouped = df2.groupby(['SUBJECT_ID'])['TEXT'].apply(lambda x: list(set(x))).reset_index()

# Merge data
result = df1.merge(df2_grouped, on=['SUBJECT_ID'], how='left')
cleaned_codes = result["ICD9_CODE"].apply(
    lambda x: [code for code in x if pd.notna(code)] if isinstance(x, list) else []
)
mlb = MultiLabelBinarizer()
# Fit and transform the list column
self_harm_icd_encod = pd.DataFrame(
    mlb.fit_transform(cleaned_codes),
    columns=mlb.classes_,
    dtype=int
)
icd9_self_harm = r"E9500|E9501|E9502|E9503|E9504|E9505|E9506|E9507|E9509|E9530|E9538|E954|E9550|E9554|E9559|E956|E9570|E9571|E9572|E9579|E9580|E9581|E9583|E9585|E9588|E9589|E959"
col_to_keep = [col for col in self_harm_icd_encod.columns if not re.match(icd9_self_harm, str(col))]
self_harm_icd_encod = self_harm_icd_encod[col_to_keep]
result["self_harm_icd_encod"] = list(self_harm_icd_encod.values) 
result["labels"] = result["ICD9_CODE"].apply(
    lambda x: int(any(re.search(icd9_self_harm, str(code)) for code in x if pd.notna(code))) 
    if isinstance(x, list) else 0
)

result['word_count'] = result['TEXT'].astype(str).str.split().str.len()
df_sorted = result.sort_values('word_count').reset_index(drop=True)

df_sorted.to_pickle("./icd_note_word.pkl")
print(f"Length of df: {len(df_sorted)}")