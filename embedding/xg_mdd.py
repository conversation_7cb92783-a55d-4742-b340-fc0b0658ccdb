import re
import sys
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.preprocessing import MultiLabelBinarizer
import xgboost as xgb
np.random.seed(42)

file_path = "./datasets/1.4/DIAGNOSES_ICD.csv"
df = pd.read_csv(file_path)

print(f"Length of icd_unique: {df['ICD9_CODE'].nunique()}")
df = df.groupby(['SUBJECT_ID', 'HADM_ID']).agg({ "ROW_ID": list,'SEQ_NUM': list, 'ICD9_CODE': list,}).reset_index()

cleaned_codes = df["ICD9_CODE"].apply(
    lambda x: [code for code in x if pd.notna(code)] if isinstance(x, list) else []
)

mlb = MultiLabelBinarizer()
# Fit and transform the list column
one_hot_encoding = pd.DataFrame(
    mlb.fit_transform(cleaned_codes),
    columns=mlb.classes_,
    dtype=int
)

keep_mask = ~one_hot_encoding.duplicated()
# Apply the mask to both DataFrames
one_hot_encoding = one_hot_encoding[keep_mask].reset_index(drop=True)
df = df[keep_mask].reset_index(drop=True)
df["one_hot_encoding"] = list(one_hot_encoding.values) 
# Define MDD codes
icd9_mdd = r"E9500|E9501|E9502|E9503|E9504|E9505|E9506|E9507|E9509|E9530|E9538|E954|E9550|E9554|E9559|E956|E9570|E9571|E9572|E9579|E9580|E9581|E9583|E9585|E9588|E9589|E959"
df["has_mdd"] = df["ICD9_CODE"].apply(
    lambda x: int(any(re.search(icd9_mdd, str(code)) for code in x if pd.notna(code))) 
    if isinstance(x, list) else 0
)
l = sum(df["has_mdd"])
print(f"Length of has_mdd code: {l}")

# Print class distribution before balancing
print(f"Original class distribution:")
print(f"Class 0: {sum(df['has_mdd'] == 0)}")
print(f"Class 1: {sum(df['has_mdd'] == 1)}")

# Balance the dataset
# Get indices for each class
class_0_indices = df[df['has_mdd'] == 0].index
class_1_indices = df[df['has_mdd'] == 1].index

# Randomly sample 3740 rows from class 0 to match class 1
n_minority_samples = len(class_1_indices) 
sampled_class_0_indices = np.random.choice(class_0_indices, size=n_minority_samples, replace=False)

# Combine indices for balanced dataset
balanced_indices = np.concatenate([sampled_class_0_indices, class_1_indices])

# Create balanced dataframes
df_balanced = df.loc[balanced_indices].reset_index(drop=True)
one_hot_encoding_balanced = one_hot_encoding.loc[balanced_indices].reset_index(drop=True)

# Verify the balance
print(f"\nBalanced class distribution:")
print(f"Class 0: {sum(df_balanced['has_mdd'] == 0)}")
print(f"Class 1: {sum(df_balanced['has_mdd'] == 1)}")

# Save balanced dataset
df_balanced.to_csv("test_balanced.csv", index=False)
#  one_hot_encoding_balanced = one_hot_encoding_balanced.loc[:, one_hot_encoding_balanced.sum() > 100]
# Prepare balanced data for modeling
x = np.asarray(one_hot_encoding_balanced)
y = df_balanced["has_mdd"]


scale_pos_weight = (len(y) - sum(y)) / sum(y)
# Define XGBoost classifier
xgb_classifier = xgb.XGBClassifier(
    objective='binary:logistic',
    n_estimators=100,
    max_depth=6,  
    learning_rate=0.01,
    reg_alpha=1.0,
    reg_lambda=1.0,
    random_state=42,
    colsample_bytree=0.6
    )

# Stratified 10-Fold Cross Validation
print("\nPerforming Stratified 100-Fold Cross Validation...")
skf = StratifiedKFold(n_splits=100, shuffle=True, random_state=42)

# Cross-validation scores
cv_scores_accuracy = cross_val_score(xgb_classifier, x, y, cv=skf, scoring='accuracy', n_jobs=-1)

print(f"Average accuracy: {cv_scores_accuracy.mean()}")
print(f"\nIndividual fold scores:")
print(f"Accuracy per fold: {cv_scores_accuracy}")
# print(f"Precision per fold: {cv_scores_precision}")
