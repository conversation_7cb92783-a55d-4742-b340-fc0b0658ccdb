import os
os.environ["CUDA_VISIBLE_DEVICES"]="0"
print(f'CUDA_VISIBLE_DEVICES {os.environ["CUDA_VISIBLE_DEVICES"]}')
import pandas as pd
import numpy as np
from tqdm import tqdm
from sentence_transformers import SentenceTransformer
import pickle

# Load the SentenceTransformer model
print("Loading Qwen/Qwen3-Embedding-8B...")
model = SentenceTransformer('Qwen/Qwen3-Embedding-8B')
print("Model loaded successfully")

# Load your dataframe
df = pd.read_pickle('./icd_note_word.pkl')

print("Generating embeddings...")

# Initialize columns if they don't exist
if "individual_embeddings" not in df.columns:
    df["individual_embeddings"] = [None] * len(df)
if "average_embedding" not in df.columns:
    df["average_embedding"] = [None] * len(df)

# Set batch size for processing large lists
BATCH_SIZE = 1 # Adjust this based on your GPU memory

# Files for incremental saving
INDIVIDUAL_EMBEDDINGS_FILE = './individual_embeddings_8b.pkl'
AVERAGE_EMBEDDINGS_FILE = './average_embeddings_8b.pkl'
PROGRESS_FILE = './progress_8b.txt'

# Load existing embeddings if they exist
try:
    with open(INDIVIDUAL_EMBEDDINGS_FILE, 'rb') as f:
        individual_embeddings_dict = pickle.load(f)
    print(f"Loaded {len(individual_embeddings_dict)} existing individual embeddings")
except FileNotFoundError:
    individual_embeddings_dict = {}
    print("Starting fresh - no existing individual embeddings found")

try:
    with open(AVERAGE_EMBEDDINGS_FILE, 'rb') as f:
        average_embeddings_dict = pickle.load(f)
    print(f"Loaded {len(average_embeddings_dict)} existing average embeddings")
except FileNotFoundError:
    average_embeddings_dict = {}
    print("Starting fresh - no existing average embeddings found")

# Load progress if it exists (stored as set of processed SUBJECT_IDs)
try:
    with open(PROGRESS_FILE, 'rb') as f:
        processed_subject_ids = pickle.load(f)
    print(f"Resuming - already processed {len(processed_subject_ids)} SUBJECT_IDs")
except FileNotFoundError:
    processed_subject_ids = set()
    print("Starting from the beginning")

def process_text_list_in_batches(text_list, batch_size):
    """Process a list of texts in batches and return all embeddings"""
    try:
        all_embeddings = []
        for i in range(0, len(text_list), batch_size):
            batch = text_list[i:i + batch_size]
            batch_embeddings = model.encode(batch)
            all_embeddings.extend(batch_embeddings)
        return np.array(all_embeddings)

    except:
        try:
            all_embeddings = []
            for i in range(0, len(text_list), int(batch_size/2)):
                batch = text_list[i:i + int(batch_size/2)]  # Fixed: use halved batch size
                batch_embeddings = model.encode(batch)
                all_embeddings.extend(batch_embeddings)
            return np.array(all_embeddings)

        except:
            # Get embedding dimension from model and return -1 filled embeddings
            try:
                sample_embedding = model.encode(["sample"])
                embedding_dim = len(sample_embedding[0])
                fallback_embeddings = [[-1] * embedding_dim for _ in text_list]
                return np.array(fallback_embeddings)
            except:
                # If even sample encoding fails, return default dimension
                fallback_embeddings = [[-1] * 768 for _ in text_list]
                return np.array(fallback_embeddings)

def save_embeddings_incrementally(subject_id, individual_emb, average_emb):
    """Save only the new embeddings for the current SUBJECT_ID"""
    # Update dictionaries using SUBJECT_ID as key
    individual_embeddings_dict[subject_id] = individual_emb
    average_embeddings_dict[subject_id] = average_emb
    
    # Save dictionaries
    with open(INDIVIDUAL_EMBEDDINGS_FILE, 'wb') as f:
        pickle.dump(individual_embeddings_dict, f)
    
    with open(AVERAGE_EMBEDDINGS_FILE, 'wb') as f:
        pickle.dump(average_embeddings_dict, f)
    
    # Save progress (set of processed SUBJECT_IDs)
    processed_subject_ids.add(subject_id)
    with open(PROGRESS_FILE, 'wb') as f:
        pickle.dump(processed_subject_ids, f)

# Process each row individually
for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing rows"):
    subject_id = row['SUBJECT_ID']
    
    # Skip if already processed
    if subject_id in processed_subject_ids:
        continue
        
    text = row['TEXT']
    
    if isinstance(text, list) and len(text) > 0:
        text_embeddings = process_text_list_in_batches(text, BATCH_SIZE)
        # Store individual embeddings for each element in the list
        individual_emb = text_embeddings.tolist()

        # Average the embeddings
        avg_embedding = np.mean(text_embeddings, axis=0)
        average_emb = avg_embedding.tolist()

    elif isinstance(text, str):
        # For single strings
        embedding = model.encode([text])[0]

        # For single strings, individual embedding is the same as the single embedding
        individual_emb = [embedding.tolist()]
        average_emb = embedding.tolist()

    else:
        # Handle empty or None values with zero embedding
        embedding_dim = model.get_sentence_embedding_dimension()
        zero_embedding = np.zeros(embedding_dim)

        individual_emb = [zero_embedding.tolist()]
        average_emb = zero_embedding.tolist()
    
    # Save only the new embeddings incrementally using SUBJECT_ID
    save_embeddings_incrementally(subject_id, individual_emb, average_emb)

# Final step: Merge embeddings back into dataframe and save complete result
print("Merging embeddings back into dataframe...")
for idx, row in df.iterrows():
    subject_id = row['SUBJECT_ID']
    if subject_id in individual_embeddings_dict:
        df.at[idx, "individual_embeddings"] = individual_embeddings_dict[subject_id]
    if subject_id in average_embeddings_dict:
        df.at[idx, "average_embedding"] = average_embeddings_dict[subject_id]

# Save final complete dataframe
df.to_pickle('./icd_with_embedding_8b.plk')

print(f"Saved dataframe with {len(df)} rows")
print(f"Individual embeddings column contains lists of embeddings for each text element")
print(f"Average embedding column contains single averaged embedding per row")
print(f"Used batch processing for lists with more than 32 elements (batch size: {BATCH_SIZE})")
print("Processing completed with incremental saving")

print("Cleaned up temporary files")